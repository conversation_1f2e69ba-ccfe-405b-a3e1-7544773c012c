from ultralytics import YOLO
import numpy as np
from PIL import Image
import io

def inference(image):
    """
    Perform pest detection inference on an image

    Args:
        image: Can be PIL Image, numpy array, or bytes

    Returns:
        tuple: (annotated_image, classes_dict, detected_class_indices)
    """
    try:
        # Load the model
        model = YOLO('best.pt')

        # Handle different input types
        if isinstance(image, bytes):
            # Convert bytes to PIL Image
            image = Image.open(io.BytesIO(image))
        elif isinstance(image, np.ndarray):
            # Convert numpy array to PIL Image
            image = Image.fromarray(image)
        # If it's already a PIL Image, use it as is

        # Run inference with lower confidence threshold for better detection
        results = model(image, conf=0.25)

        # Initialize return values
        infer = np.array(image)  # Default to original image
        classes = {}
        namesInfer = []

        # Process results
        for r in results:
            # Get annotated image with bounding boxes
            infer = r.plot()
            # Get class names dictionary
            classes = r.names
            # Get detected class indices
            if r.boxes is not None and len(r.boxes) > 0:
                namesInfer = r.boxes.cls.tolist()
            else:
                namesInfer = []

        return infer, classes, namesInfer

    except Exception as e:
        print(f"Error in inference: {e}")
        # Return default values in case of error
        if isinstance(image, bytes):
            image = Image.open(io.BytesIO(image))
        elif isinstance(image, np.ndarray):
            image = Image.fromarray(image)

        return np.array(image), {}, []
