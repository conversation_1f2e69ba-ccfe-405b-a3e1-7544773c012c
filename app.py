import streamlit as st
import os
from PIL import Image
import numpy as np
from infer import inference
from chat import chatbot, getweatherdata, class_info_dict
import io

# Page configuration
st.set_page_config(
    page_title="Pest Detection System",
    layout="wide",
    initial_sidebar_state="collapsed"
)

# Custom CSS for consistent white container theme
st.markdown("""
    <style>
    /* Main container styling */
    .main {
        padding: 1rem;
        background-color: #1a1a1a;
    }
    
    /* White container for all content */
    .stApp > main > div > div > div {
        background-color: white;
        border-radius: 10px;
        padding: 2rem;
        margin: 1rem 0;
    }
    
    /* Title styling */
    h1 {
        color: #333;
        text-align: center;
        margin-bottom: 2rem;
    }
    
    h3 {
        color: #333;
    }
    
    /* Button styling */
    .stButton > button {
        background-color: #0066cc;
        color: white;
        border-radius: 5px;
        padding: 0.5rem 1rem;
        border: none;
        font-weight: 500;
        width: 100%;
    }
    
    .stButton > button:hover {
        background-color: #0052a3;
    }
    
    /* File uploader styling */
    .stFileUploader {
        background-color: #f8f9fa;
        border: 2px dashed #dee2e6;
        border-radius: 8px;
        padding: 1rem;
    }
    
    /* Chat container styling */
    .chat-container {
        background-color: #f5f5f5;
        border: 1px solid #e0e0e0;
        border-radius: 10px;
        padding: 1rem;
        height: 400px;
        overflow-y: auto;
    }
    
    /* Info box styling */
    .stAlert {
        background-color: #e3f2fd !important;
        color: #1976d2 !important;
        border-radius: 8px !important;
        border: 1px solid #bbdefb !important;
        padding: 1rem !important;
        margin: 1rem 0 !important;
        position: relative !important;
        z-index: 10 !important;
    }
    
    /* Column styling */
    [data-testid="column"] {
        background-color: white;
        padding: 1.5rem;
        border-radius: 10px;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    /* Dark background for main app */
    [data-testid="stAppViewContainer"] {
        background-color: #1a1a1a;
    }
    
    [data-testid="stHeader"] {
        background-color: #1a1a1a;
    }
    
    /* Image styling */
    .stImage {
        border-radius: 8px;
        overflow: hidden;
    }
    
    /* Weather info styling */
    .weather-info {
        background-color: #f0f7ff;
        border: 1px solid #d0e3ff;
        border-radius: 8px;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Language selector styling */
    .stSelectbox {
        margin-bottom: 1rem;
    }
    </style>
""", unsafe_allow_html=True)

# Initialize session state
if 'processed' not in st.session_state:
    st.session_state.processed = False
if 'chat_history' not in st.session_state:
    st.session_state.chat_history = []
if 'detected_pests' not in st.session_state:
    st.session_state.detected_pests = []
if 'image_bytes' not in st.session_state:
    st.session_state.image_bytes = None
if 'language' not in st.session_state:
    st.session_state.language = "English"
if 'weather_day' not in st.session_state:
    st.session_state.weather_day = "Today"
if 'gdd' not in st.session_state:
    st.session_state.gdd = 150  # Default GDD value

# Title
title_text = "害虫検出システム" if st.session_state.language == "Japanese" else "Pest Detection System"
st.markdown(f"<h1>{title_text}</h1>", unsafe_allow_html=True)

# Create main container
with st.container():
    # Create two columns with spacing
    col1, spacer, col2 = st.columns([5, 0.5, 5])
    
    # Left column - Image upload and processing
    with col1:
        # Preview section
        preview_text = "🐛 プレビュー" if st.session_state.language == "Japanese" else "🐛 Preview"
        st.markdown(f"### {preview_text}")
        
        # File uploader with custom styling
        upload_text = "ファイルを選択" if st.session_state.language == "Japanese" else "Choose file"
        help_text = "害虫を検出するために作物の画像をアップロードしてください" if st.session_state.language == "Japanese" else "Upload an image of crops to detect pests"
        
        uploaded_file = st.file_uploader(
            upload_text, 
            type=["jpg", "jpeg", "png"], 
            key="file_uploader",
            help=help_text
        )
        
        if uploaded_file is not None:
            # Read and store image
            image = Image.open(uploaded_file)
            image_np = np.array(image)
            st.session_state.image_bytes = image_np
            
            # Display uploaded image
            caption_text = "アップロードされた画像" if st.session_state.language == "Japanese" else "Uploaded Image"
            st.image(image, caption=caption_text, use_container_width=True)
            
            # Process button
            process_text = "処理" if st.session_state.language == "Japanese" else "Process"
            if st.button(process_text, type="primary", use_container_width=True):
                with st.spinner("処理中..." if st.session_state.language == "Japanese" else "Processing image..."):
                    # Perform pest detection
                    infer_img, classes, detected_classes = inference(st.session_state.image_bytes)
                    
                    # Store detected pests
                    st.session_state.detected_pests = []
                    for cls_idx in detected_classes:
                        pest_name = classes[int(cls_idx)]
                        if pest_name in class_info_dict:
                            st.session_state.detected_pests.append({
                                "pest": pest_name,
                                "info": class_info_dict[pest_name]
                            })
                    
                    st.session_state.processed = True
                    st.session_state.annotated_image = infer_img
        
        # Display processed image if available
        if st.session_state.processed and hasattr(st.session_state, 'annotated_image'):
            processed_text = "処理済み画像" if st.session_state.language == "Japanese" else "Processed Image"
            st.markdown(f"### {processed_text}")
            
            # Create caption with detected pests
            if st.session_state.detected_pests:
                pests_list = [p["pest"] for p in st.session_state.detected_pests]
                if st.session_state.language == "Japanese":
                    caption = f"**検出された害虫:** {', '.join(pests_list)}"
                else:
                    caption = f"**Detected Pests:** {', '.join(pests_list)}"
            else:
                caption = "**害虫は検出されませんでした**" if st.session_state.language == "Japanese" else "**No pests detected**"
            
            # Display annotated image
            st.image(st.session_state.annotated_image, caption=caption, use_container_width=True)
    
    # Spacer column
    with spacer:
        st.empty()
    
    # Right column - Chatbot interface
    with col2:
        assistant_text = "💬 害虫対策アシスタント" if st.session_state.language == "Japanese" else "💬 Pest Control Assistant"
        st.markdown(f"### {assistant_text}")
        
        # Language selector
        language = st.selectbox(
            "Select Language / 言語を選択",
            ["English", "Japanese"],
            index=0 if st.session_state.language == "English" else 1,
            key="language_selector"
        )
        st.session_state.language = language
        
        if st.session_state.processed and st.session_state.detected_pests:
            # Weather day selector
            weather_label = "天気予報の日を選択:" if st.session_state.language == "Japanese" else "Select weather forecast day:"
            day_options = ["Today", "Tomorrow", "2 days", "3 days", "4 days"]
            if st.session_state.language == "Japanese":
                day_options_jp = ["今日", "明日", "2日後", "3日後", "4日後"]
                selected_day_idx = st.selectbox(weather_label, range(len(day_options)), 
                                               format_func=lambda x: day_options_jp[x])
                st.session_state.weather_day = day_options[selected_day_idx]
            else:
                st.session_state.weather_day = st.selectbox(weather_label, day_options)
            
            # GDD input
            gdd_label = "成長度日 (GDD):" if st.session_state.language == "Japanese" else "Growing Degree Days (GDD):"
            st.session_state.gdd = st.number_input(gdd_label, min_value=0, max_value=1000, value=150)
            
            # Get weather data
            weather_data = getweatherdata(st.session_state.weather_day)
            
            # Display weather info
            weather_title = "🌤️ 現在の天気情報" if st.session_state.language == "Japanese" else "🌤️ Current Weather Info"
            st.markdown(f"""
                <div class="weather-info">
                    <strong>{weather_title}</strong><br>
                    {"日付" if st.session_state.language == "Japanese" else "Date"}: {weather_data[0]}<br>
                    {"気温" if st.session_state.language == "Japanese" else "Temperature"}: {weather_data[1]}°C<br>
                    {"湿度" if st.session_state.language == "Japanese" else "Humidity"}: {weather_data[2]}%<br>
                    {"風速" if st.session_state.language == "Japanese" else "Wind Speed"}: {weather_data[3]} m/s
                </div>
            """, unsafe_allow_html=True)
            
            # Show detected pests
            pests_text = ', '.join([p['pest'] for p in st.session_state.detected_pests])
            
            if st.session_state.language == "Japanese":
                label = "検出された害虫："
            else:
                label = "Detected Pests:"
                
            st.markdown(
                f"""
                <div style='background-color: #e3f2fd; 
                           color: #1976d2; 
                           padding: 1rem; 
                           border-radius: 8px; 
                           border: 1px solid #bbdefb;
                           margin-bottom: 1rem;'>
                    <strong>{label}</strong> {pests_text}
                </div>
                """, 
                unsafe_allow_html=True
            )
            
            # Quick action buttons
            if st.session_state.language == "Japanese":
                st.markdown("**どのようにお手伝いできますか？**")
                btn1_text = "この害虫について教えて"
                btn2_text = "農薬の推奨事項"
                btn3_text = "最適な散布条件"
            else:
                st.markdown("**How can we assist you?**")
                btn1_text = "Tell me about this pest"
                btn2_text = "Pesticide recommendations"
                btn3_text = "Optimal application conditions"
            
            col_btn1, col_btn2, col_btn3 = st.columns(3)
            with col_btn1:
                if st.button(btn1_text, use_container_width=True):
                    pest = st.session_state.detected_pests[0]["pest"]
                    pest_info = st.session_state.detected_pests[0]["info"]
                    
                    st.session_state.chat_history.append({
                        "role": "user",
                        "content": btn1_text
                    })
                    
                    response = chatbot(
                        pest_info, 
                        st.session_state.chat_history, 
                        btn1_text,
                        weather_data,
                        st.session_state.gdd,
                        st.session_state.language
                    )
                    
                    st.session_state.chat_history.append({
                        "role": "assistant",
                        "content": response
                    })
                    st.rerun()
            
            with col_btn2:
                if st.button(btn2_text, use_container_width=True):
                    pest = st.session_state.detected_pests[0]["pest"]
                    pest_info = st.session_state.detected_pests[0]["info"]
                    
                    query = f"What pesticides do you recommend for {pest}?" if st.session_state.language == "English" else f"{pest}に対してどの農薬を推奨しますか？"
                    
                    st.session_state.chat_history.append({
                        "role": "user",
                        "content": query
                    })
                    
                    response = chatbot(
                        pest_info, 
                        st.session_state.chat_history, 
                        query,
                        weather_data,
                        st.session_state.gdd,
                        st.session_state.language
                    )
                    
                    st.session_state.chat_history.append({
                        "role": "assistant",
                        "content": response
                    })
                    st.rerun()
            
            with col_btn3:
                if st.button(btn3_text, use_container_width=True):
                    pest = st.session_state.detected_pests[0]["pest"]
                    pest_info = st.session_state.detected_pests[0]["info"]
                    
                    query = "What are the optimal weather conditions for pesticide application?" if st.session_state.language == "English" else "農薬散布の最適な気象条件は何ですか？"
                    
                    st.session_state.chat_history.append({
                        "role": "user",
                        "content": query
                    })
                    
                    response = chatbot(
                        pest_info, 
                        st.session_state.chat_history, 
                        query,
                        weather_data,
                        st.session_state.gdd,
                        st.session_state.language
                    )
                    
                    st.session_state.chat_history.append({
                        "role": "assistant",
                        "content": response
                    })
                    st.rerun()
            
            # Chat messages container
            st.markdown("---")
            
            chat_container = st.container(height=400)
            with chat_container:
                for message in st.session_state.chat_history:
                    with st.chat_message(message["role"]):
                        st.markdown(message["content"])
            
            # Chat input
            placeholder = "メッセージを入力..." if st.session_state.language == "Japanese" else "Type a message..."
            user_question = st.chat_input(placeholder, key="chat_input")
            
            if user_question:
                st.session_state.chat_history.append({
                    "role": "user",
                    "content": user_question
                })
                
                with st.spinner("考え中..." if st.session_state.language == "Japanese" else "Thinking..."):
                    pest = st.session_state.detected_pests[0]["pest"]
                    pest_info = st.session_state.detected_pests[0]["info"]
                    
                    response = chatbot(
                        pest_info, 
                        st.session_state.chat_history, 
                        user_question,
                        weather_data,
                        st.session_state.gdd,
                        st.session_state.language
                    )
                    
                    st.session_state.chat_history.append({
                        "role": "assistant",
                        "content": response
                    })
                
                st.rerun()
        
        else:
            # Empty state
            if st.session_state.language == "Japanese":
                empty_message = "👈 会話を開始するには、画像をアップロードして処理してください"
            else:
                empty_message = "👈 Please upload and process an image to start the conversation"
                
            st.markdown(
                f"""
                <div style='text-align: center; padding: 3rem; background-color: #f5f5f5; border-radius: 10px; margin-top: 2rem;'>
                    <p style='color: #666; font-size: 1.1rem;'>
                        {empty_message}
                    </p>
                </div>
                """, 
                unsafe_allow_html=True
            )

# Add footer spacing
st.markdown("<br><br>", unsafe_allow_html=True)
