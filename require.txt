# Core dependencies for the Pest Detection System

# Streamlit for web interface
streamlit>=1.28.0

# AI/ML Libraries
ultralytics>=8.0.0
torch>=1.8.0
torchvision>=0.9.0
numpy>=1.18.5

# Image processing
Pillow>=7.1.2
opencv-python>=4.6.0

# LangChain for chatbot functionality
langchain>=0.1.0
langchain-groq>=0.1.0

# Weather and location services
requests>=2.23.0
geopy>=2.0.0

# Environment variables
python-dotenv>=0.19.0

# Additional utilities
PyYAML>=5.3.1
tqdm>=4.41.0
pandas>=1.1.4
matplotlib>=3.3.0
scipy>=1.4.1

# Optional: For Roboflow integration (if needed)
# roboflow>=1.0.0