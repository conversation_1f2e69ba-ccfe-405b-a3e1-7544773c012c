{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "execution_count": 1, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "id": "EF2CHmT_XCKz", "outputId": "6a0215f8-adc7-4ebf-e5d4-1c9506420cb4"}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting roboflow\n", "  Downloading roboflow-1.2.0-py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from roboflow) (2025.6.15)\n", "Collecting idna==3.7 (from roboflow)\n", "  Downloading idna-3.7-py3-none-any.whl.metadata (9.9 kB)\n", "Requirement already satisfied: cycler in /usr/local/lib/python3.11/dist-packages (from roboflow) (0.12.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.4.8)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (from roboflow) (3.10.0)\n", "Requirement already satisfied: numpy>=1.18.5 in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.0.2)\n", "Collecting opencv-python-headless==********* (from roboflow)\n", "  Downloading opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (20 kB)\n", "Requirement already satisfied: Pillow>=7.1.2 in /usr/local/lib/python3.11/dist-packages (from roboflow) (11.2.1)\n", "Collecting pillow-heif<2 (from roboflow)\n", "  Downloading pillow_heif-1.0.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.6 kB)\n", "Collecting pillow-avif-plugin<2 (from roboflow)\n", "  Downloading pillow_avif_plugin-1.5.2-cp311-cp311-manylinux_2_28_x86_64.whl.metadata (2.1 kB)\n", "Requirement already satisfied: python-dateutil in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.9.0.post0)\n", "Collecting python-dotenv (from roboflow)\n", "  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.32.3)\n", "Requirement already satisfied: six in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.17.0)\n", "Requirement already satisfied: urllib3>=1.26.6 in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.4.0)\n", "Requirement already satisfied: tqdm>=4.41.0 in /usr/local/lib/python3.11/dist-packages (from roboflow) (4.67.1)\n", "Requirement already satisfied: PyYAML>=5.3.1 in /usr/local/lib/python3.11/dist-packages (from roboflow) (6.0.2)\n", "Requirement already satisfied: requests-toolbelt in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.0.0)\n", "Collecting filetype (from roboflow)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (1.3.2)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (4.58.4)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->roboflow) (3.4.2)\n", "Downloading roboflow-1.2.0-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.8/86.8 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading idna-3.7-py3-none-any.whl (66 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m66.8/66.8 kB\u001b[0m \u001b[31m5.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (49.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.9/49.9 MB\u001b[0m \u001b[31m19.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pillow_avif_plugin-1.5.2-cp311-cp311-manylinux_2_28_x86_64.whl (4.2 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.2/4.2 MB\u001b[0m \u001b[31m112.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pillow_heif-1.0.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (4.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m4.9/4.9 MB\u001b[0m \u001b[31m118.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)\n", "Installing collected packages: pillow-avif-plugin, filetype, python-dotenv, pillow-heif, opencv-python-headless, idna, roboflow\n", "  Attempting uninstall: opencv-python-headless\n", "    Found existing installation: opencv-python-headless *********\n", "    Uninstalling opencv-python-headless-*********:\n", "      Successfully uninstalled opencv-python-headless-*********\n", "  Attempting uninstall: idna\n", "    Found existing installation: idna 3.10\n", "    Uninstalling idna-3.10:\n", "      Successfully uninstalled idna-3.10\n", "Successfully installed filetype-1.2.0 idna-3.7 opencv-python-headless-********* pillow-avif-plugin-1.5.2 pillow-heif-1.0.0 python-dotenv-1.1.1 roboflow-1.2.0\n", "Collecting ultralytics\n", "  Downloading ultralytics-8.3.161-py3-none-any.whl.metadata (37 kB)\n", "Requirement already satisfied: numpy>=1.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.0.2)\n", "Requirement already satisfied: matplotlib>=3.3.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (3.10.0)\n", "Requirement already satisfied: opencv-python>=4.6.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (*********)\n", "Requirement already satisfied: pillow>=7.1.2 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (11.2.1)\n", "Requirement already satisfied: pyyaml>=5.3.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (6.0.2)\n", "Requirement already satisfied: requests>=2.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.32.3)\n", "Requirement already satisfied: scipy>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (1.15.3)\n", "Requirement already satisfied: torch>=1.8.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.6.0+cu124)\n", "Requirement already satisfied: torchvision>=0.9.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (0.21.0+cu124)\n", "Requirement already satisfied: tqdm>=4.64.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (4.67.1)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from ultralytics) (5.9.5)\n", "Requirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from ultralytics) (9.0.0)\n", "Requirement already satisfied: pandas>=1.1.4 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.2.2)\n", "Collecting ultralytics-thop>=2.0.0 (from ultralytics)\n", "  Downloading ultralytics_thop-2.0.14-py3-none-any.whl.metadata (9.4 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2025.6.15)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (4.14.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==******** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==******** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==******** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==********** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==******** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==********** (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)\n", "Downloading ultralytics-8.3.161-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m24.5 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cublas_cu12-********-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m5.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m124.8 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m81.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m47.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-********-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m2.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-********-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m5.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-**********-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m16.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-********-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m8.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-**********-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m114.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ultralytics_thop-2.0.14-py3-none-any.whl (26 kB)\n", "Installing collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, ultralytics-thop, ultralytics\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 *********\n", "    Uninstalling nvidia-cusolver-cu12-*********:\n", "      Successfully uninstalled nvidia-cusolver-cu12-*********\n", "Successfully installed nvidia-cublas-cu12-******** nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-******** nvidia-cufft-cu12-******** nvidia-curand-cu12-********** nvidia-cusolver-cu12-******** nvidia-cusparse-cu12-********** nvidia-nvjitlink-cu12-12.4.127 ultralytics-8.3.161 ultralytics-thop-2.0.14\n"]}], "source": ["!pip install roboflow\n", "!pip install ultralytics"]}, {"cell_type": "code", "source": ["from roboflow import Roboflow\n", "rf = Roboflow(api_key=\"YOUR API KEY\")\n", "project = rf.workspace(\"laktharu\").project(\"rice-pests-ztbeq\")\n", "version = project.version(1)\n", "dataset = version.download(\"yolov8\")\n", ""], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "y45fQ4OtXFHM", "outputId": "9593f70d-4c67-43b4-fd1a-c7c340f15d4e"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["loading Roboflow workspace...\n", "loading Roboflow project...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Downloading Dataset Version Zip in Rice-Pests-1 to yolov8:: 100%|██████████| 194630/194630 [00:03<00:00, 61957.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n", "Extracting Dataset Version Zip to Rice-Pests-1 in yolov8:: 100%|██████████| 10470/10470 [00:01<00:00, 7832.15it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Creating new Ultralytics Settings v0.0.6 file ✅ \n", "View Ultralytics Settings with 'yolo settings' or at '/root/.config/Ultralytics/settings.json'\n", "Update Settings with 'yolo settings key=value', i.e. 'yolo settings runs_dir=path/to/dir'. For help see https://docs.ultralytics.com/quickstart/#ultralytics-settings.\n"]}]}, {"cell_type": "code", "source": ["from ultralytics import YOLO\n", "\n", "model = YOLO(\"yolov8n.pt\")\n", "results = model.train(data=\"/content/Rice-Pests-1/data.yaml\", epochs=50, batch = 16)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "ZlNuTTw2XFEr", "outputId": "6f578e80-6d6f-4ea3-af6a-03ac8a365b20"}, "execution_count": 3, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolov8n.pt to 'yolov8n.pt'...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 6.25M/6.25M [00:00<00:00, 121MB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Ultralytics 8.3.161 🚀 Python-3.11.13 torch-2.6.0+cu124 CUDA:0 (Tesla T4, 15095MiB)\n", "\u001b[34m\u001b[1mengine/trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=/content/Rice-Pests-1/data.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=50, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolov8n.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs/detect/train, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "Downloading https://ultralytics.com/assets/Arial.ttf to '/root/.config/Ultralytics/Arial.ttf'...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 755k/755k [00:00<00:00, 18.5MB/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["Overriding model.yaml nc=80 with nc=6\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]                 \n", "  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]                \n", "  2                  -1  1      7360  ultralytics.nn.modules.block.C2f             [32, 32, 1, True]             \n", "  3                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  4                  -1  2     49664  ultralytics.nn.modules.block.C2f             [64, 64, 2, True]             \n", "  5                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  6                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  8                  -1  1    460288  ultralytics.nn.modules.block.C2f             [256, 256, 1, True]           \n", "  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1     37248  ultralytics.nn.modules.block.C2f             [192, 64, 1]                  \n", " 16                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]                \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    123648  ultralytics.nn.modules.block.C2f             [192, 128, 1]                 \n", " 19                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 22        [15, 18, 21]  1    752482  ultralytics.nn.modules.head.Detect           [6, [64, 128, 256]]           \n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Model summary: 129 layers, 3,012,018 parameters, 3,012,002 gradients, 8.2 GFLOPs\n", "\n", "Transferred 319/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mAMP: \u001b[0mrunning Automatic Mixed Precision (AMP) checks...\n", "Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt to 'yolo11n.pt'...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 5.35M/5.35M [00:00<00:00, 88.1MB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mAMP: \u001b[0mchecks passed ✅\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 1423.5±466.2 MB/s, size: 44.4 KB)\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning /content/Rice-Pests-1/train/labels... 3922 images, 11 backgrounds, 0 corrupt: 100%|██████████| 3922/3922 [00:01<00:00, 2359.64it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mNew cache created: /content/Rice-Pests-1/train/labels.cache\n", "WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 598, len(boxes) = 5987. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 735.2±628.1 MB/s, size: 36.0 KB)\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning /content/Rice-Pests-1/valid/labels... 1046 images, 3 backgrounds, 0 corrupt: 100%|██████████| 1046/1046 [00:01<00:00, 799.04it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mval: \u001b[0mNew cache created: /content/Rice-Pests-1/valid/labels.cache\n", "WARNING ⚠️ Box and segment counts should be equal, but got len(segments) = 208, len(boxes) = 1683. To resolve this only boxes will be used and all segments will be removed. To avoid this please supply either a detect or segment dataset, not a detect-segment mixed dataset.\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Plotting labels to runs/detect/train/labels.jpg... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m AdamW(lr=0.001, momentum=0.9) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias(decay=0.0)\n", "Image sizes 640 train, 640 val\n", "Using 2 dataloader workers\n", "Logging results to \u001b[1mruns/detect/train\u001b[0m\n", "Starting training for 50 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       1/50      2.13G      1.416      2.953      1.549         28        640: 100%|██████████| 246/246 [01:09<00:00,  3.55it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:10<00:00,  3.05it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.43      0.355      0.321      0.164\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       2/50      2.82G      1.456      2.283      1.543          9        640: 100%|██████████| 246/246 [01:07<00:00,  3.64it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.45it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.428      0.431       0.37      0.178\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       3/50      2.84G      1.484      2.049      1.559          4        640: 100%|██████████| 246/246 [01:06<00:00,  3.71it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.64it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.529      0.429      0.434      0.201\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       4/50      2.85G       1.49      1.867      1.555          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.64it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.60it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.632      0.559      0.601      0.314\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       5/50      2.87G      1.444      1.688      1.516          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.56it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.669      0.604      0.662      0.362\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       6/50      2.88G      1.396      1.552      1.484          4        640: 100%|██████████| 246/246 [01:06<00:00,  3.67it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.64it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.694      0.672      0.736      0.401\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       7/50      2.91G      1.385       1.45      1.469          3        640: 100%|██████████| 246/246 [01:06<00:00,  3.70it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.97it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.756      0.699      0.747      0.418\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       8/50      2.91G      1.359      1.378      1.451          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.05it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.739      0.698      0.751      0.431\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["       9/50      2.94G      1.354      1.336      1.438          6        640: 100%|██████████| 246/246 [01:06<00:00,  3.70it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.57it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.768      0.762       0.81       0.47\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      10/50      2.95G      1.317      1.264      1.411         10        640: 100%|██████████| 246/246 [01:06<00:00,  3.71it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.57it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.808      0.756      0.822      0.475\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      11/50      2.97G      1.308      1.237      1.411          3        640: 100%|██████████| 246/246 [01:06<00:00,  3.69it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.62it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.844      0.794      0.853      0.511\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      12/50      2.98G      1.308      1.194      1.412          2        640: 100%|██████████| 246/246 [01:07<00:00,  3.66it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.57it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.836      0.778      0.845      0.499\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      13/50      3.01G      1.283      1.144      1.385         11        640: 100%|██████████| 246/246 [01:07<00:00,  3.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.50it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.873      0.782      0.856      0.513\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      14/50      3.02G      1.268      1.103      1.376          6        640: 100%|██████████| 246/246 [01:07<00:00,  3.64it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.86      0.816      0.863      0.527\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      15/50      3.04G      1.257      1.088      1.372          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.66it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.05it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.858      0.812      0.883       0.55\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      16/50      3.05G      1.241      1.062       1.36          6        640: 100%|██████████| 246/246 [01:07<00:00,  3.63it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.96it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.847      0.812      0.863      0.544\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      17/50      3.08G      1.225      1.023      1.347          6        640: 100%|██████████| 246/246 [01:07<00:00,  3.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.61it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.857      0.824      0.878      0.539\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      18/50      3.09G      1.225      1.018      1.349          8        640: 100%|██████████| 246/246 [01:07<00:00,  3.66it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.61it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.892      0.845      0.905      0.576\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      19/50      3.11G      1.205     0.9637      1.336          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.62it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.54it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.886      0.848      0.898      0.555\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      20/50      3.12G      1.199     0.9614      1.332          5        640: 100%|██████████| 246/246 [01:07<00:00,  3.62it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.65it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.868      0.838      0.893      0.568\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      21/50      3.15G       1.18     0.9391      1.313          4        640: 100%|██████████| 246/246 [01:08<00:00,  3.60it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.73it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.891      0.853      0.909      0.578\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      22/50      3.15G      1.168     0.9019      1.308          6        640: 100%|██████████| 246/246 [01:07<00:00,  3.67it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.58it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.888      0.862      0.914      0.582\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      23/50      3.18G      1.152     0.8989      1.305          7        640: 100%|██████████| 246/246 [01:06<00:00,  3.69it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.64it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.894      0.876       0.92      0.594\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      24/50      3.19G      1.162     0.8922      1.309          3        640: 100%|██████████| 246/246 [01:06<00:00,  3.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.70it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.901      0.859      0.907       0.58\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      25/50      3.21G      1.161     0.8894      1.304          7        640: 100%|██████████| 246/246 [01:06<00:00,  3.70it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.96it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.903      0.882      0.918      0.596\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      26/50      3.22G      1.131     0.8441      1.289          6        640: 100%|██████████| 246/246 [01:07<00:00,  3.65it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.78it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683        0.9      0.868      0.913      0.589\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      27/50      3.25G      1.127     0.8411      1.287          5        640: 100%|██████████| 246/246 [01:06<00:00,  3.69it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.08it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.891      0.879      0.922      0.607\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      28/50      3.26G      1.123     0.8189      1.278          8        640: 100%|██████████| 246/246 [01:05<00:00,  3.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.94it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.887      0.875       0.91      0.588\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      29/50      3.28G      1.117     0.8231      1.278          6        640: 100%|██████████| 246/246 [01:08<00:00,  3.58it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.09it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.913      0.885      0.931      0.616\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      30/50      3.29G      1.116     0.8024      1.273          3        640: 100%|██████████| 246/246 [01:09<00:00,  3.54it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.08it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.92      0.891      0.936      0.624\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      31/50      3.32G      1.092     0.7983      1.257         12        640: 100%|██████████| 246/246 [01:08<00:00,  3.58it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.00it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.919      0.896      0.935      0.614\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      32/50      3.32G      1.083     0.7747      1.261          5        640: 100%|██████████| 246/246 [01:08<00:00,  3.59it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.97it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.92      0.881      0.929      0.616\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      33/50      3.35G      1.083     0.7704      1.253          5        640: 100%|██████████| 246/246 [01:07<00:00,  3.67it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:07<00:00,  4.14it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.92      0.899      0.934      0.632\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      34/50      3.36G      1.079     0.7539      1.257          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.67it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.10it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.919      0.884      0.933      0.626\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      35/50      3.39G      1.074     0.7395      1.248          5        640: 100%|██████████| 246/246 [01:07<00:00,  3.66it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:07<00:00,  4.19it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.926       0.89      0.929      0.626\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      36/50      3.39G      1.068     0.7431      1.239          8        640: 100%|██████████| 246/246 [01:07<00:00,  3.63it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.10it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.92      0.904      0.942      0.634\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      37/50      3.42G      1.049     0.7148      1.238          2        640: 100%|██████████| 246/246 [01:06<00:00,  3.72it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.58it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.93      0.896      0.936      0.634\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      38/50      3.43G      1.037     0.7027      1.224          3        640: 100%|██████████| 246/246 [01:05<00:00,  3.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.77it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.93      0.902      0.938       0.63\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      39/50      3.45G      1.041     0.7049      1.228          2        640: 100%|██████████| 246/246 [01:05<00:00,  3.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.72it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.912      0.919      0.943      0.641\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      40/50      3.46G      1.037     0.7018      1.226          6        640: 100%|██████████| 246/246 [01:05<00:00,  3.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.70it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.925      0.917      0.942      0.647\n", "Closing dataloader mosaic\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      41/50      3.49G     0.9987     0.5604       1.23          2        640: 100%|██████████| 246/246 [01:04<00:00,  3.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.79it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.922       0.91      0.939      0.637\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      42/50       3.5G      0.979     0.5347      1.212          3        640: 100%|██████████| 246/246 [01:02<00:00,  3.92it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.89it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.934      0.902      0.943      0.647\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      43/50      3.52G     0.9655     0.5197      1.198          2        640: 100%|██████████| 246/246 [01:03<00:00,  3.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.68it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.926      0.918      0.944      0.649\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      44/50      3.53G     0.9533     0.5109      1.194          2        640: 100%|██████████| 246/246 [01:03<00:00,  3.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.61it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.94      0.912      0.943      0.649\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      45/50      3.55G     0.9343     0.5014      1.187          2        640: 100%|██████████| 246/246 [01:03<00:00,  3.87it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:07<00:00,  4.14it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.93      0.912      0.942      0.653\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      46/50      3.56G     0.9278     0.4938       1.18          2        640: 100%|██████████| 246/246 [01:03<00:00,  3.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.83it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.935      0.911      0.943      0.655\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      47/50      3.59G     0.9218      0.485       1.17          4        640: 100%|██████████| 246/246 [01:03<00:00,  3.90it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:09<00:00,  3.66it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.939      0.916      0.947      0.658\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      48/50       3.6G     0.9059     0.4727      1.161          2        640: 100%|██████████| 246/246 [01:04<00:00,  3.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  4.08it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.931      0.917      0.944      0.658\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      49/50      3.62G     0.8986     0.4729       1.16          3        640: 100%|██████████| 246/246 [01:07<00:00,  3.63it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.93      0.927      0.947      0.661\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      50/50      3.63G     0.8951     0.4642      1.156          2        640: 100%|██████████| 246/246 [01:03<00:00,  3.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:08<00:00,  3.69it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683       0.93      0.923      0.947      0.664\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "50 epochs completed in 1.052 hours.\n", "Optimizer stripped from runs/detect/train/weights/last.pt, 6.3MB\n", "Optimizer stripped from runs/detect/train/weights/best.pt, 6.3MB\n", "\n", "Validating runs/detect/train/weights/best.pt...\n", "Ultralytics 8.3.161 🚀 Python-3.11.13 torch-2.6.0+cu124 CUDA:0 (Tesla T4, 15095MiB)\n", "Model summary (fused): 72 layers, 3,006,818 parameters, 0 gradients, 8.1 GFLOPs\n"]}, {"output_type": "stream", "name": "stderr", "text": ["                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 33/33 [00:10<00:00,  3.05it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all       1046       1683      0.929      0.923      0.946      0.663\n", "     brown-planthopper        116        429      0.847      0.825      0.881      0.538\n", "      green-leafhopper        208        216      0.947      0.968      0.977      0.724\n", "           leaf-folder        187        243      0.974      0.939      0.968      0.586\n", "              rice-bug        191        238      0.946      0.945       0.94       0.66\n", "            stem-borer        206        223       0.96      0.987      0.986      0.855\n", "          whorl-maggot        135        334      0.901      0.873      0.926      0.618\n", "Speed: 0.3ms preprocess, 2.1ms inference, 0.0ms loss, 2.2ms postprocess per image\n", "Results saved to \u001b[1mruns/detect/train\u001b[0m\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "hOghxOk6Y0c1"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model  = YOLO(\"/content/runs/detect/train/weights/best.pt\")\n", "\n", "results = model.predict(\"/content/Rice-Pests-1/test/images\", save = True, stream = True)\n", "\n", "for result in results:\n", "  pass"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "XPNO6FiZY0Z7", "outputId": "b896e0df-0099-4198-81e5-6f7b01391c84"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "image 1/261 /content/Rice-Pests-1/test/images/69502_jpg.rf.726660489bd522b3330b68726d0df39b.jpg: 640x640 1 green-leafhopper, 10.1ms\n", "image 2/261 /content/Rice-Pests-1/test/images/69505_jpg.rf.b03fa0f5d4e7f134d2ca20823417f628.jpg: 640x640 1 green-leafhopper, 9.8ms\n", "image 3/261 /content/Rice-Pests-1/test/images/69563_jpg.rf.eee4f3ed1bcb97aaa82b6ab38fc84660.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 4/261 /content/Rice-Pests-1/test/images/69720_jpg.rf.6f26ed060eb6cd75f01ff7f5adee129d.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 5/261 /content/Rice-Pests-1/test/images/69736_jpg.rf.cc3bb5b61b2929e2161fc986f11f5293.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 6/261 /content/Rice-Pests-1/test/images/69788_jpg.rf.eccfa42afcb4fdd8aec21b767b6c8203.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 7/261 /content/Rice-Pests-1/test/images/70024_jpg.rf.4d5490c45f94256c16a910f6259099fb.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 8/261 /content/Rice-Pests-1/test/images/70178_jpg.rf.6bcd717e6038dba1992a4786a9a2115e.jpg: 640x640 2 green-leafhoppers, 7.6ms\n", "image 9/261 /content/Rice-Pests-1/test/images/70179_jpg.rf.3f8d4e235ebfdec8697b7f620bdda435.jpg: 640x640 1 green-leafhopper, 7.3ms\n", "image 10/261 /content/Rice-Pests-1/test/images/70259_jpg.rf.bc5e20fdded0f85be48689a98a0ea7b5.jpg: 640x640 1 green-leafhopper, 7.4ms\n", "image 11/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-107-jpg_5aea44b9-6712-471b-85e4-38a4ef3d74f2_jpg.rf.d440726058ac5729bc04e27e3fc3d1a3.jpg: 640x640 1 brown-planthopper, 7.3ms\n", "image 12/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-124-jpg_6268d7d6-46af-4db0-9b8e-a1741c57dfbe_jpg.rf.56f8b713ce694d888f19b676b1cbafae.jpg: 640x640 4 brown-planthoppers, 7.4ms\n", "image 13/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-131-jpg_e71387dc-90c5-47f8-9226-e3e82a29f427_jpg.rf.6b48c216569ef34af2d6e58ba4df1e4c.jpg: 640x640 2 brown-planthoppers, 7.4ms\n", "image 14/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-141-jpg_ce35924e-c680-476d-9244-31e0cf03cb48_jpg.rf.2e78e0926b347f7b9d2ca11eabe20821.jpg: 640x640 1 brown-planthopper, 7.4ms\n", "image 15/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-146-jpg_22c97da9-d789-4fd1-9464-b3b1bd6ce4ed_jpg.rf.e9801deb51754e3202edd30774335be7.jpg: 640x640 2 brown-planthoppers, 7.4ms\n", "image 16/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-177-jpg_7295675c-3385-4e41-8cd7-9d988844cf09_jpg.rf.1d584c6ab876ffa4a404485e5462eb97.jpg: 640x640 1 brown-planthopper, 7.4ms\n", "image 17/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-186-jpg_b02dbad6-b197-4357-af19-9f5bd8128865_jpg.rf.35e2f40e86bbc8f78babd31da99db57a.jpg: 640x640 2 brown-planthoppers, 7.4ms\n", "image 18/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-195-jpg_f5e190db-f792-412f-8f56-fc09cda440ab_jpg.rf.e2280a8b5fd6ac27f9cf0c1eb9036a24.jpg: 640x640 4 brown-planthoppers, 9.0ms\n", "image 19/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-2-jpg_758b54c9-0f75-4d55-8706-fa303cd46bac_jpg.rf.823c7e5adc7722dd4a68b5a36df5b304.jpg: 640x640 34 brown-planthoppers, 7.4ms\n", "image 20/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-210-jpg_3bbd732d-098f-478f-8f28-b973f4a0c83c_jpg.rf.5c3e84b88b19c7756254dc2ea7df47e6.jpg: 640x640 14 brown-planthoppers, 7.4ms\n", "image 21/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-211-jpg_2c8f6f50-9197-4273-82cb-9c0a3075c84d_jpg.rf.deaaa203c08985c135a2c75e9438497e.jpg: 640x640 4 brown-planthoppers, 7.4ms\n", "image 22/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-213-jpg_a07f0b13-f17e-43c1-8b16-1f1ed51fc126_jpg.rf.031907b71a449f6ad79a6b23ad8e8b45.jpg: 640x640 9 brown-planthoppers, 7.4ms\n", "image 23/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-218-jpg_24ff87e2-f5fa-404d-8c6d-75a8f1a67845_jpg.rf.aded1a8d255fae25fff01b96c4d1bd75.jpg: 640x640 1 brown-planthopper, 1 rice-bug, 7.2ms\n", "image 24/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-219-jpg_9edf7694-0a35-4539-b157-c82ce769f08c_jpg.rf.d724675ef73b98a377b9cbbb78b3238b.jpg: 640x640 2 brown-planthoppers, 7.2ms\n", "image 25/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-224-jpg_e2fa428f-45eb-46a7-ac68-51568a888959_jpg.rf.014cbee6bd09bf33796bd7582145705d.jpg: 640x640 1 brown-planthopper, 1 rice-bug, 8.5ms\n", "image 26/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-233-jpg_c2c79799-dab5-45d7-b55e-33294b4ed3fb_jpg.rf.980fe9496572c59e11d562a4f4e631d6.jpg: 640x640 5 brown-planthoppers, 11.9ms\n", "image 27/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-234-jpg_d1799b3c-d978-4f1e-a0af-dad95b26e5e8_jpg.rf.dc8045fa2a6416e1da43cbe79fa7b29c.jpg: 640x640 1 brown-planthopper, 6.7ms\n", "image 28/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-3-jpeg_687856c0-88fe-4c2a-885a-3567b2b7ca36_jpeg_jpg.rf.7695f596e78498a62b7897f0e0042bbd.jpg: 640x640 4 brown-planthoppers, 6.7ms\n", "image 29/261 /content/Rice-Pests-1/test/images/BROWN-PLANTHOPPER_original_bhopper-83-jpg_d17e4ec8-e3aa-4a28-90f3-fdbf56e2f59d_jpg.rf.0417f1d247b3dc4dcba2109479815d72.jpg: 640x640 1 brown-planthopper, 1 rice-bug, 10.0ms\n", "image 30/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69497-jpg_e5d91216-6994-447f-9290-07d276dfac40_jpg.rf.a6bedf4cb578184def5febd79dc50e59.jpg: 640x640 1 brown-planthopper, 1 green-leafhopper, 10.5ms\n", "image 31/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69505-jpg_a56d140e-30aa-41ee-9327-e8f8f2813851_jpg.rf.e9190c757f7c9ad9163c0dd4fb550b8c.jpg: 640x640 1 green-leafhopper, 12.0ms\n", "image 32/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69533-jpg_be150d5b-16ab-4aee-9092-af3ce889d5c9_jpg.rf.ed3e994dfd8188181214a3c6dc78e342.jpg: 640x640 1 green-leafhopper, 10.2ms\n", "image 33/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69534-jpg_fcc11de9-0c4c-41b7-ac7c-58aab820cc71_jpg.rf.d8d7343b0833bfe051d7f47d22a7a844.jpg: 640x640 1 green-leafhopper, 8.8ms\n", "image 34/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69541-jpg_84dec61d-e282-4377-9c58-920759e87afd_jpg.rf.32e0bb8209beb969b87768233957df44.jpg: 640x640 1 green-leafhopper, 6.8ms\n", "image 35/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69553-jpg_fb6af153-b9e1-49bc-bc4c-72f61ea07820_jpg.rf.7a7bfc30bbf6c95e04a9050f2a59e2c8.jpg: 640x640 1 green-leafhopper, 7.2ms\n", "image 36/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69561-jpg_1927b30d-986e-4a15-bbd9-a660f92a90eb_jpg.rf.d5c25a2218190abb08d5e1b7d6082172.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 37/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69566-jpg_2971ac02-3f9b-4297-8e78-a6c852f22776_jpg.rf.cdca3a3b7d4d050030064c398d51dd47.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 38/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69572-jpg_88c79ff5-81b9-4b3c-a30d-ee8cdf42e400_jpg.rf.8e236fc6475322c7bce7350db5423654.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 39/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69583-jpg_6a9bb05c-c3ed-4634-b628-5cf0723bed39_jpg.rf.11c9bac61a94274d6bb467ed098bd1fa.jpg: 640x640 1 green-leafhopper, 6.8ms\n", "image 40/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69588-jpg_5dba22fc-45aa-4103-bd2f-2572b628b987_jpg.rf.18d078f285b02023e175dfec5fc2ecc8.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 41/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69590-jpg_02f8297c-5c18-45c0-9c9f-e4cd539e1a1a_jpg.rf.2538a20a2f09a21dcfb193ef0174796e.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 42/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69603-jpg_87a5be31-36c8-42e2-9e45-c6f02e7f6764_jpg.rf.c7346a2101d8b820fd39407fabe3b2ce.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 43/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69615-jpg_8c4c691a-948e-43d4-8eee-6e98f4e58c82_jpg.rf.9f7510936c1d3680faadef2f432ad0d5.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 44/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69636-jpg_f1416f74-ece9-4e84-921a-076a37a6af0b_jpg.rf.6b0586ab7312b6404686fdd233903485.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 45/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69642-jpg_f881a060-5d05-48e2-a8eb-fc75e2efca5f_jpg.rf.57ee3edb3c5f2b344a7bc020c1c2f139.jpg: 640x640 1 green-leafhopper, 6.7ms\n", "image 46/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69648-jpg_379fd8cd-300c-4481-97f1-92e5ee550674_jpg.rf.5e1e5859e1f6af78c0ef12b007018c67.jpg: 640x640 1 green-leafhopper, 7.5ms\n", "image 47/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69648-jpg_c78aacf8-fecf-4f51-bd71-e8f8e271f834_jpg.rf.0837eced23eb18b6d043f0342fb77d9f.jpg: 640x640 1 green-leafhopper, 8.4ms\n", "image 48/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69653-jpg_1a4704a6-cade-4653-a86a-deb857e9bef3_jpg.rf.8f7fc29c35c68c62c59d445c22217f09.jpg: 640x640 1 green-leafhopper, 6.8ms\n", "image 49/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69653-jpg_a9c60d4f-ffa5-4b3f-9f66-524ef9830010_jpg.rf.4c3d7dc68cc8d46794b92ae13e2a2f25.jpg: 640x640 1 green-leafhopper, 11.4ms\n", "image 50/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69666-jpg_2e133afe-248e-4318-b36c-672d1764c5da_jpg.rf.410c64e0ff7f39a5704d61524dc6a0b2.jpg: 640x640 1 green-leafhopper, 7.1ms\n", "image 51/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69666-jpg_6df71687-445f-4b9d-ab16-4f5251e18187_jpg.rf.58888ef5f1f3669573efa30334a31a50.jpg: 640x640 1 green-leafhopper, 6.8ms\n", "image 52/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69708-jpg_ab0b1375-b124-416d-8a4d-cdde4823ebdb_jpg.rf.f33c424b9d6a8ac8ffbfb1d0e79603c4.jpg: 640x640 1 green-leafhopper, 6.3ms\n", "image 53/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69713-jpg_6e8dd0aa-eaf1-4b05-98fb-8d1f8612838a_jpg.rf.3b80369b737edd90276de11852322c28.jpg: 640x640 3 green-leafhoppers, 6.3ms\n", "image 54/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69736-jpg_4e361bba-e8ff-4425-8fa5-fe3c425873d4_jpg.rf.0c89a5de73a5547c513eabcff9c30cb9.jpg: 640x640 1 green-leafhopper, 6.5ms\n", "image 55/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_69741-jpg_d6d77ba8-4a77-4ed2-85be-1fc74f41f1be_jpg.rf.cd1a4e48e5e6da54eae9eacf0b750730.jpg: 640x640 1 green-leafhopper, 7.0ms\n", "image 56/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70015-jpg_77cb91c9-fdce-435f-9cd0-50d08733a69f_jpg.rf.b72e8401bc8ab5463ec7133d35d177b2.jpg: 640x640 2 green-leafhoppers, 6.2ms\n", "image 57/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70017-jpg_2b11bba8-7674-4fcc-bfb6-810245596083_jpg.rf.b99070f3ffdc7113cc1e2e574eb92c97.jpg: 640x640 1 green-leafhopper, 6.4ms\n", "image 58/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70027-jpg_e28c5a0c-fa3c-4b9f-8d2b-31e55e969b8a_jpg.rf.d5d597295987f29865c335cee23defb6.jpg: 640x640 1 green-leafhopper, 6.5ms\n", "image 59/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70043-jpg_92d75ab9-14c0-45f3-876a-ea447e7374da_jpg.rf.7b9c422020f8ed560fa458383d85ae7a.jpg: 640x640 1 green-leafhopper, 6.6ms\n", "image 60/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70072-jpg_918bdbd4-5d9c-4916-a514-4993f54f971a_jpg.rf.ffd7ae4cf1bde3abb223da9318c520e5.jpg: 640x640 1 green-leafhopper, 6.3ms\n", "image 61/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70095-jpg_2969da3e-4502-4640-a54f-b63dcac60557_jpg.rf.ac0c1f717c58e7f1dffb5c7e174eaca8.jpg: 640x640 1 green-leafhopper, 6.4ms\n", "image 62/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70168-jpg_e4cb126f-e625-489a-a36b-99f5b6943ff3_jpg.rf.fe6a50794d79b30667d8499aa31de3ef.jpg: 640x640 1 green-leafhopper, 6.1ms\n", "image 63/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70179-jpg_b562a073-37c6-483b-ad6c-c2f1c1721ec6_jpg.rf.275fdecfcbc652e134ea9e54be75ddce.jpg: 640x640 1 green-leafhopper, 7.1ms\n", "image 64/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70191-jpg_eeab18ed-397f-4fac-92c5-a5083d4b7a26_jpg.rf.4f0fff9b0981f896330d595009d04281.jpg: 640x640 2 green-leafhoppers, 6.3ms\n", "image 65/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70203-jpg_15c21242-2cdc-403f-a2f4-7d818b3e9302_jpg.rf.eb615e00262c6ba5ea7cd1d6d442e9b6.jpg: 640x640 1 green-leafhopper, 6.8ms\n", "image 66/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70220-jpg_01690b3c-ea9a-429b-a84c-bfd5f64b5e89_jpg.rf.bd88b967607aaeda79c93116489e0b7c.jpg: 640x640 1 green-leafhopper, 6.2ms\n", "image 67/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70223-jpg_adaecc98-45f3-4e02-911f-cc2af62db80f_jpg.rf.54b0e1d857118dab51d4a135b801b436.jpg: 640x640 1 green-leafhopper, 6.2ms\n", "image 68/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70241-jpg_afa382a9-6dd9-4ef6-be3d-6b5e398927e1_jpg.rf.45a99eac6b9c2558ba3ebdf59bcbd95e.jpg: 640x640 2 green-leafhoppers, 6.1ms\n", "image 69/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70256-jpg_30e423de-e65f-4aa0-b202-3c21636fe684_jpg.rf.2fef317b8aa49ca196a9a3a1daf64c01.jpg: 640x640 1 green-leafhopper, 5.9ms\n", "image 70/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70256-jpg_9f87a4bd-0c82-4b57-836f-99d8d9a7e955_jpg.rf.4c59943bd1b918b6bcdceb594e6f5919.jpg: 640x640 1 green-leafhopper, 6.0ms\n", "image 71/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70271-jpg_83dc9e2b-2f10-432e-b062-df04b90d8364_jpg.rf.b20487d7e8def44c41223148f69104bd.jpg: 640x640 1 green-leafhopper, 6.3ms\n", "image 72/261 /content/Rice-Pests-1/test/images/GREEN-LEAFHOPPERS_original_70313-jpg_c59f644c-761a-4b90-b229-08fcdf2dbb53_jpg.rf.2a38eabf355caab22abfcff2bf14aed6.jpg: 640x640 1 green-leafhopper, 6.1ms\n", "image 73/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-11-jpg_4b55d55f-04ef-4b9a-9863-70be1e739eac_jpg.rf.b7a7aeda5fecb0a3c4877739194e10fd.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 74/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-11-jpg_51e1cc21-a25c-47ad-a808-eb0214bf890a_jpg.rf.72bd5e2818881d121cd47d60ccd39ec2.jpg: 640x640 1 leaf-folder, 6.0ms\n", "image 75/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-15-jpg_cc5279c5-a5e9-42f0-9807-8cae8892f979_jpg.rf.985027dbf55e27e514ea0fdf67137488.jpg: 640x640 1 leaf-folder, 6.2ms\n", "image 76/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-16-jpg_0a874a25-6298-493a-b596-ea432fee54cc_jpg.rf.4edf2c69c2af66d26e68735ba4623a44.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 77/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-2-jpg_296af903-860c-4d63-88f5-40a6cd2257b9_jpg.rf.c2b9877e15c69769a77436e5dc94f2ae.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 78/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-2-jpg_93234341-a9e6-4c42-a3d0-21b320a8c1f3_jpg.rf.58cf82b00c6cbe41aa6284fa51dd7fac.jpg: 640x640 1 leaf-folder, 7.0ms\n", "image 79/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-256-jpg_e90ff702-6e6c-48ae-87b7-aa1f21310055_jpg.rf.a7736d140f2302088189399b59c87b97.jpg: 640x640 2 leaf-folders, 6.3ms\n", "image 80/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-257-jpg_d7a0df76-7e49-4784-b99a-d563a36ca2cb_jpg.rf.9bc639c70fc4fc48d1132a70e5fdf620.jpg: 640x640 2 leaf-folders, 6.8ms\n", "image 81/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-26-jpg_553ef180-cd9c-4820-be3f-d516bdd5292b_jpg.rf.d80e07e71d824e645dc0ca40cfa262f2.jpg: 640x640 1 leaf-folder, 6.7ms\n", "image 82/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-260-jpg_207fd35b-05bd-4517-a5f3-5e4764d8b3da_jpg.rf.c28e2b69d8d37e794e2e6586e05f3c1a.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 83/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-261-jpg_8aecc9fe-aa85-44c0-bf65-f24689d5de60_jpg.rf.12488af77b914dff109d0f5dd16c9446.jpg: 640x640 2 leaf-folders, 6.2ms\n", "image 84/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-262-jpg_8f87311f-032e-4f47-92cd-06236c9ffaab_jpg.rf.10722d7873ee3a37383b91dbb446f6d8.jpg: 640x640 10 leaf-folders, 6.3ms\n", "image 85/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-262-jpg_a4b66d1d-0374-499f-9303-54f98b871732_jpg.rf.a2aefc95e816c74b7341b0e5e36a83cb.jpg: 640x640 9 leaf-folders, 6.2ms\n", "image 86/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-265-jpg_56cb0cdf-486a-484f-866e-deaf97cdec38_jpg.rf.715798c835640e0546d5eb4059f74ab3.jpg: 640x640 2 leaf-folders, 6.4ms\n", "image 87/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-265-jpg_a6d62355-fa2c-4c1c-b8ec-12afab875e89_jpg.rf.3281104b59006b588a19f33f713ae24a.jpg: 640x640 2 leaf-folders, 6.5ms\n", "image 88/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-270-jpg_b48e2cd9-3158-4732-9c5f-a45d990fbf0b_jpg.rf.4136f72254b1e0b6cd7dd98cb764d67e.jpg: 640x640 1 leaf-folder, 7.0ms\n", "image 89/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-272-jpg_a69ebab1-0d0e-404c-b593-3270bd3710be_jpg.rf.8569a9b1f5361202665c8f85a9a91067.jpg: 640x640 1 leaf-folder, 12.1ms\n", "image 90/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-272-jpg_f86c48bf-1b79-4b50-bfab-d66e69c26715_jpg.rf.15e79b0415d1fbe02e72359b09295181.jpg: 640x640 1 leaf-folder, 7.6ms\n", "image 91/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-273-jpg_69681443-2371-4352-938f-81f27f060efe_jpg.rf.545e44e15efcfb031af069dc07602203.jpg: 640x640 1 leaf-folder, 6.6ms\n", "image 92/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-273-jpg_96a469b3-62fa-4111-b2b5-db16af91c95f_jpg.rf.064c09800dd4e62876a838e5d4a79e41.jpg: 640x640 2 leaf-folders, 6.6ms\n", "image 93/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-278-jpg_24e336c2-9c3c-4686-868c-c6b7e80f7667_jpg.rf.d9c5d16da11f811060064fd4462c1c8b.jpg: 640x640 1 leaf-folder, 6.5ms\n", "image 94/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-290-jpg_46d63b41-05d5-4c8e-ad90-ca68d95f0909_jpg.rf.e5b4ea86f6d655ae7034e3326b72e48b.jpg: 640x640 1 leaf-folder, 6.7ms\n", "image 95/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-292-jpg_77e9336e-77db-4f1f-ab90-121649f0cdf1_jpg.rf.204353ef4f53785be2737fce0a9a8cfa.jpg: 640x640 1 leaf-folder, 6.6ms\n", "image 96/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-299-jpg_8d1fd9b6-874e-40b3-a7c6-adb1f78b3a5d_jpg.rf.66eb8258b00d8e3eca7762566b7129c1.jpg: 640x640 1 leaf-folder, 1 rice-bug, 10.7ms\n", "image 97/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-30-jpg_11ac06b0-ccf2-452f-9efb-6cc674c6cc53_jpg.rf.86099dc40ff0a76b5bc32c38f0440a61.jpg: 640x640 1 leaf-folder, 7.6ms\n", "image 98/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-306-jpg_51560678-448c-464f-bff4-54d105e74b40_jpg.rf.2fd06f9515ac5f7489333156b4f505c3.jpg: 640x640 1 leaf-folder, 7.1ms\n", "image 99/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-307-jpg_e44c0b4b-4d64-45c8-8465-beb32671bf66_jpg.rf.5c99854f6a2285785569f7ce7a8cd0a1.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 100/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-309-jpg_7654e21a-b005-4204-8254-5d2571f160fa_jpg.rf.94858fc8c6956958aa4bf0bf38de04e9.jpg: 640x640 2 leaf-folders, 6.2ms\n", "image 101/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-315-jpg_5074cbc2-a1da-4fbb-bba7-72d67a48d822_jpg.rf.dad38496b74f305fca1c6e7c71529047.jpg: 640x640 2 leaf-folders, 6.3ms\n", "image 102/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-315-jpg_d919fbfe-d091-4cb4-a11f-1fa69702e0e2_jpg.rf.88dad532289444d0852845eaaf6be2b4.jpg: 640x640 2 leaf-folders, 6.3ms\n", "image 103/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-318-jpg_8027016f-11a9-4ada-8bc3-f5e0c3d34e6a_jpg.rf.ed2da429c73271ab58d28e795b6fbd18.jpg: 640x640 1 leaf-folder, 6.0ms\n", "image 104/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-32-jpg_64bd4c80-e34e-484b-8b44-accaad8ce507_jpg.rf.82afe922068befe60401f24321f60e3f.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 105/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-334-jpg_aba2e00d-e1df-4d45-97cc-469840dee7a3_jpg.rf.ad80aed3a5b5a543107b182d4dba92c4.jpg: 640x640 1 leaf-folder, 6.4ms\n", "image 106/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-335-jpg_b17e2cc0-2d16-41bc-8640-fc1ed49c9309_jpg.rf.b9952f465f00e25ac67d39f412bed429.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 107/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-335-jpg_b61cdbf2-82e8-4ab0-876f-78f6d87c90da_jpg.rf.c4ca43d930da14b9ae76db36812518bf.jpg: 640x640 1 leaf-folder, 6.6ms\n", "image 108/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-35-jpg_9ea28478-ca02-4e18-bb81-d8938d296d5d_jpg.rf.ceca29d57c0d402832327a5ecb540cc3.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 109/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-36-jpg_3c023783-de3d-4b37-9dc3-1a017c101fdd_jpg.rf.efc391a9aa39026fe4913253ebe88c49.jpg: 640x640 4 leaf-folders, 8.4ms\n", "image 110/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-38-jpg_da37b243-78b9-4b9f-849e-da6688572894_jpg.rf.f89dfaf201447f003559770012a729e5.jpg: 640x640 2 leaf-folders, 6.3ms\n", "image 111/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-40-jpg_dc5ac209-9e23-40bc-900a-51c79c33dd7a_jpg.rf.e043dc4bec0232e52d5ee2576009c7b8.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 112/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-41-jpg_0d916966-d423-485d-8ef9-c575f5b605e2_jpg.rf.d096a5c54a44fc94f20bc4cbdbd261e9.jpg: 640x640 1 leaf-folder, 8.2ms\n", "image 113/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-41-jpg_b2575a52-e46d-4125-92d6-010fad403890_jpg.rf.0950d472f09b04574843ddf0ced97ece.jpg: 640x640 1 leaf-folder, 7.3ms\n", "image 114/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-49-jpg_817dea29-caea-4265-84ae-b33b46a0cdb9_jpg.rf.0530638fc4c3b12da76a447eff566f42.jpg: 640x640 1 leaf-folder, 6.7ms\n", "image 115/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-50-jpg_558ba6d6-025f-4f9a-8f54-f97cd1a8086a_jpg.rf.fed5cfb75cbf25e5c8cf9911ce420eca.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 116/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-54-jpg_96243555-e2fb-4972-aafb-fd7ac9ebde21_jpg.rf.70990b5fdd92acb430bfb317e8838e8b.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 117/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-56-jpg_48463033-3e06-44d2-86fb-02427fce2994_jpg.rf.1e5b98b13319e82bf919e2c968ef5545.jpg: 640x640 5 leaf-folders, 6.2ms\n", "image 118/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-56-jpg_5a3b1135-8004-44ea-9459-fce4baa7c291_jpg.rf.604c07fabf9c395614d6d177b2fb1fec.jpg: 640x640 4 leaf-folders, 8.0ms\n", "image 119/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-62-jpg_0bd5ec3c-5856-4f4e-8c8c-4a5588789a02_jpg.rf.10bc98e7d7b5890fb41e1b52b5ba05d1.jpg: 640x640 1 leaf-folder, 6.6ms\n", "image 120/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-64-jpg_44497659-1396-451a-b05c-05b3f7189d79_jpg.rf.1ea56186b0ec2c16e9952afe7c384d2c.jpg: 640x640 1 leaf-folder, 6.3ms\n", "image 121/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-69-jpg_3ae77cc8-bd4d-4bcb-b6f6-159b0a9e768f_jpg.rf.81f9e1d9ff2af1ce413272c4d4679605.jpg: 640x640 1 leaf-folder, 6.2ms\n", "image 122/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-69-jpg_46f103aa-4482-4262-b6c6-c50cd96a202a_jpg.rf.18e5b4d9d307e6436172ef404844fafe.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 123/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-71-jpg_35807e86-f246-4e74-bb47-54fab6bac1bc_jpg.rf.5e5b482040a998d21d0034371bfba1a1.jpg: 640x640 5 leaf-folders, 6.2ms\n", "image 124/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-73-jpg_d1e46da1-3504-47a9-b27c-00a80e525a84_jpg.rf.a1444bca0bed5ce702ef026b96d3d01f.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 125/261 /content/Rice-Pests-1/test/images/LEAF-FOLDERS_original_lfolder-9-jpg_259ed43d-21a7-4936-990b-36c97230f57a_jpg.rf.af0ca3529df2de35391b80db8708aa18.jpg: 640x640 1 leaf-folder, 6.1ms\n", "image 126/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-23-jpg_8b3c64a3-24d3-407d-aa85-e7ca49e7970d_jpg.rf.32024c116a2bef19a5ce7bc4bc63d260.jpg: 640x640 1 rice-bug, 6.5ms\n", "image 127/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-23-jpg_919bac39-034e-4163-893d-01f6d670e6b7_jpg.rf.130e10572c8400c9d142fc1ce829d5ca.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 128/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-235-jpg_1f98a53c-aa3e-413b-bdda-b30632207c6c_jpg.rf.ea3dde72b3d629e405a51fccdad1be1f.jpg: 640x640 1 rice-bug, 6.5ms\n", "image 129/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-239-jpg_78516731-3009-436c-a726-ff6b263e797f_jpg.rf.229181834c44d4050191231135283db3.jpg: 640x640 1 rice-bug, 7.1ms\n", "image 130/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-246-jpg_6ff528e7-acd5-4a93-a94e-17956bc192e9_jpg.rf.acec2150a1b9a4c3ca57703134efc7ea.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 131/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-258-jpg_a12c75c7-89c8-4a91-81cd-4bd31093501d_jpg.rf.55c6b9221c3a6f24aeed387d3d2cf935.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 132/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-259-jpg_2e58296a-166a-4a39-b826-6bc25c3701fe_jpg.rf.c78e016bd39d28ff81974644c2f8a476.jpg: 640x640 1 rice-bug, 6.5ms\n", "image 133/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-259-jpg_d252dde3-7bab-4110-a4d8-635d1df87305_jpg.rf.229b21cd9b9a4b97ec7d069f236d04ac.jpg: 640x640 1 rice-bug, 6.4ms\n", "image 134/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-260-jpg_0f69e69c-5906-4114-a59b-60e6ed44f4db_jpg.rf.892e5b86c0c0ff4575cd12d43733a02d.jpg: 640x640 1 rice-bug, 6.8ms\n", "image 135/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-262-jpg_e0c8682b-4f5e-417e-ae02-fad414c1d642_jpg.rf.88bb4cd1eadf8f742a771f7e228b037f.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 136/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-262-jpg_ee5c97b9-02a7-40b6-80a3-38445db86d00_jpg.rf.ce7f76b84c6a66beb3f92cab8c1b2525.jpg: 640x640 1 rice-bug, 6.2ms\n", "image 137/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-268-jpg_1aa7f853-9eb1-40ab-b2f2-01bf9ff13797_jpg.rf.686e1a89964b27a094789ea0b4e7366e.jpg: 640x640 1 rice-bug, 6.2ms\n", "image 138/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-269-jpg_5e8f2a85-1afe-409b-87c3-2ba2f6f817d4_jpg.rf.d3a08b75cd9bb499d48cc5900a60ed91.jpg: 640x640 3 rice-bugs, 6.1ms\n", "image 139/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-270-jpg_259cd5dc-41d4-44ba-b9da-f5553f65263d_jpg.rf.bc9c60c3404c30f51131b89c45fe8267.jpg: 640x640 2 rice-bugs, 6.5ms\n", "image 140/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-270-jpg_fcd4941a-9e6c-4970-a021-7522e669b8eb_jpg.rf.2ff80526c74f4e5b87c8e2c71344abef.jpg: 640x640 3 rice-bugs, 6.1ms\n", "image 141/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-275-jpg_738e9c66-526e-42be-9219-f23321748633_jpg.rf.9f7ae4c5b27a407540b9e6299d0432db.jpg: 640x640 1 rice-bug, 6.2ms\n", "image 142/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-277-jpg_c06a662e-fc41-46e7-80a4-2a54042a0ba5_jpg.rf.846e2ef37b143c45dc797738c7b8389d.jpg: 640x640 1 rice-bug, 8.2ms\n", "image 143/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-282-jpg_2ab86eb7-2b1d-48d0-a4d9-df4451d13600_jpg.rf.a4f312b6bc60257b73c822d8f581f967.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 144/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-286-jpg_85c3b66c-13dc-4cd5-ad19-e4b41211aff8_jpg.rf.d7ea109c2a8b01aee6460393d0338463.jpg: 640x640 1 rice-bug, 6.2ms\n", "image 145/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-297-jpg_11ffc79b-b715-4ed7-b028-d36f38134cd1_jpg.rf.967cadc4868af9c9559a70d81a3b94cf.jpg: 640x640 2 rice-bugs, 6.1ms\n", "image 146/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-300-jpg_d0feb251-1db9-4f67-8da0-ae7a16b0d340_jpg.rf.7b0e4f532da11facc61a0266548c0277.jpg: 640x640 1 rice-bug, 8.2ms\n", "image 147/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-301-jpg_282347ef-183d-4b63-b83b-22e9f79f1f38_jpg.rf.c96a1478cbc6fc6e68e0ae7258a1dca0.jpg: 640x640 1 rice-bug, 7.5ms\n", "image 148/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-303-jpg_5b3bf781-b02a-4497-b851-eb43b65fc930_jpg.rf.981c6d0283db832cb7c1896a054882ce.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 149/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-31-jpg_e8e53140-e095-483d-afe2-49b0672df4a5_jpg.rf.8a3e324a1b99ec46a3a328f4f1095bb2.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 150/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-34-jpg_f9b82331-12e9-4a0e-984a-31f4f6d4d673_jpg.rf.86d1981563cbe8e9259f3c944875e757.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 151/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-37-jpg_6a0c1a2b-f0ed-473f-8daf-1e5ac479e0a3_jpg.rf.2b1dd1b4463c431541ea9a01ede02e77.jpg: 640x640 1 rice-bug, 7.3ms\n", "image 152/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-41-jpg_40c5643f-28eb-469f-8b21-d7bf496313e6_jpg.rf.540b1e8fc059fb0e0b7115da96e7a263.jpg: 640x640 2 rice-bugs, 6.1ms\n", "image 153/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-43-jpg_afc552c4-b640-4d86-9261-8d7924109790_jpg.rf.dd101f4a104089caa2eca37bf77a494d.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 154/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-48-jpg_7cfa5dd9-d14f-4af3-8917-012794872b47_jpg.rf.e47839c5f3e451739c32ccecc14ca414.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 155/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-59-jpg_4dfebb4b-24e0-483b-a3fb-f3f4be1cb2c1_jpg.rf.f127644bc4140fd8f02b43e7e14f7388.jpg: 640x640 1 rice-bug, 10.5ms\n", "image 156/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-6-jpg_d6379500-e1e1-4534-95f5-a2d8f04615f9_jpg.rf.4994916f115048f17b96f935d64aae6e.jpg: 640x640 1 rice-bug, 11.9ms\n", "image 157/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-60-jpg_8e90bd1e-3a09-4dfd-93a4-b104e68fb36a_jpg.rf.cdab95be954da040874a18b5451edb85.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 158/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-63-jpg_f3904892-1691-443e-a272-201ec38c584c_jpg.rf.f1fb037606040095409204cbe3b6506f.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 159/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-63-jpg_f952f390-4fe0-4b44-be93-34bc308bc623_jpg.rf.7e4a4e37c186b718da92f084ea5b0122.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 160/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-71-jpg_e75abc0f-a08d-4419-b1f8-a55ee56959bf_jpg.rf.f2d566db8a575218218a9bf901ab1ba1.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 161/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-71-jpg_fc353143-9a31-4379-818d-8d76bbf9255c_jpg.rf.05a725c5855a025b2b41cfa44a3dfae6.jpg: 640x640 1 rice-bug, 6.3ms\n", "image 162/261 /content/Rice-Pests-1/test/images/RICE-BUGS_original_rbug-74-jpg_76513e89-1dea-41b9-8fcd-d8a6d699c149_jpg.rf.49b5080841c77f9745840bb7b85c4b4b.jpg: 640x640 1 rice-bug, 7.2ms\n", "image 163/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-13-jpg_2944cfd0-9ba0-414f-b4b7-c9c69d41dd6c_jpg.rf.c7aa22f6a3f48ac14b08e64cc7c68e10.jpg: 640x640 1 stem-borer, 6.5ms\n", "image 164/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-16-jpg_68cee03e-360a-4399-b115-36826ff36a55_jpg.rf.98e0afad8ba76eaa3e5a29c05c9f86ad.jpg: 640x640 6 stem-borers, 6.5ms\n", "image 165/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-16-jpg_f8297b4d-efe6-4cd0-a8ed-1856b00322c2_jpg.rf.b8c6ab7f24f0b299e7b771c017f715db.jpg: 640x640 4 stem-borers, 6.5ms\n", "image 166/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-19-jpg_b5c9d92d-fd24-40f6-8597-33b11fb4be4b_jpg.rf.b94aded1af4b80d6f867225678d74216.jpg: 640x640 1 stem-borer, 7.1ms\n", "image 167/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-2-jpg_35858243-81e7-4bab-a5e3-e08e99d069c9_jpg.rf.4df4aadbfe1a13896d2bd855ba6ba727.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 168/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-22-jpg_e2b58834-15fd-4f3e-a548-24c617115d19_jpg.rf.4f32a1c98c3fb414ae7f282d1a614913.jpg: 640x640 1 stem-borer, 6.3ms\n", "image 169/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-226-jpg_d30d83fd-1e66-4a41-99ef-46319d98b3cd_jpg.rf.cee7e76b17f3b8ea8279daf3650cf7e8.jpg: 640x640 1 stem-borer, 6.0ms\n", "image 170/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-228-jpg_42508bb7-48c1-485a-8c1d-9634a40d56f4_jpg.rf.d7c41567ff777f4b3461346097db86bd.jpg: 640x640 1 stem-borer, 6.5ms\n", "image 171/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-229-jpg_8117fea7-98c2-487f-a5cc-284e4ea0ecea_jpg.rf.462e855a6316c07142bb6f8cea1ba6cf.jpg: 640x640 1 stem-borer, 6.6ms\n", "image 172/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-237-jpg_297e03f4-d561-477f-b810-07aed8931022_jpg.rf.4087e43678e37d2a41677bcc6ba0ffe0.jpg: 640x640 1 stem-borer, 6.9ms\n", "image 173/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-237-jpg_2ce2bca5-4576-49ae-969e-c2c1973ca91c_jpg.rf.e8de00f99687a7da152794863c85c5c4.jpg: 640x640 1 stem-borer, 7.6ms\n", "image 174/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-238-jpg_d1a0667a-9140-44c6-97a8-ee1c4b88ccf5_jpg.rf.62a48f44c76d80481d53dfb8f31b11c5.jpg: 640x640 1 stem-borer, 8.2ms\n", "image 175/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-239-jpg_9b164611-ec69-4300-ba56-4edc9c3403ee_jpg.rf.ed684ea0050d132d96a05bfbb707e6d9.jpg: 640x640 6 stem-borers, 6.7ms\n", "image 176/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-241-jpg_5ea3dcd5-ffde-487c-9be7-e1b3b8a32dba_jpg.rf.bd622a421b8b43fcc6eee8313e8597fb.jpg: 640x640 1 stem-borer, 6.5ms\n", "image 177/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-245-jpg_fc123535-e707-4ec0-97b3-b29e67581fcb_jpg.rf.fc8845245fdd3c554e8d3769918a2a5e.jpg: 640x640 1 stem-borer, 7.6ms\n", "image 178/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-25-jpg_5ce38ec9-67e7-4f04-a297-3d1992f7b207_jpg.rf.e3661285ace8b1f98b59a8774c6228b1.jpg: 640x640 1 stem-borer, 7.4ms\n", "image 179/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-250-jpg_4494d8d0-009a-4e81-b25c-c834591945f4_jpg.rf.31fa44001bdd9b7f80cb24f97d948637.jpg: 640x640 1 stem-borer, 6.8ms\n", "image 180/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-251-jpg_2b6be5a3-bc7b-453b-b0a3-e73c2fdd313c_jpg.rf.41c0ec91494e5369787a61d402eebf8a.jpg: 640x640 1 stem-borer, 6.9ms\n", "image 181/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-259-jpg_eb02432e-0eb5-4105-9f4e-dc576eb51e6c_jpg.rf.94bb021d62d3360d1688ba923000aa23.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 182/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-264-jpg_367ebc45-7d9d-44df-bb07-545bba1b0623_jpg.rf.6248c26aeb99d6ed45c6d9b9f7ba6dd2.jpg: 640x640 1 stem-borer, 6.3ms\n", "image 183/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-265-jpg_fd3f04aa-97f6-437c-8d5f-885a92a59462_jpg.rf.76e5a2fc258f989322a6c8606d1f436d.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 184/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-268-jpg_1a129bf8-d62b-4d2d-b08d-4f79c2e95027_jpg.rf.995afde38ad9846e347616b44507e510.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 185/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-27-jpg_0d72bf6a-03ca-4efe-8f87-24a53514d311_jpg.rf.1d28242ea6c8dd420c93101ba15b7e75.jpg: 640x640 1 stem-borer, 6.0ms\n", "image 186/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-275-jpg_cfb426d3-d6d7-40b0-8e6c-95d16441f521_jpg.rf.bb20a90b4d7904e284dde009b8c6a1f5.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 187/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-277-jpg_0c7f448f-adfe-43ee-8c9d-b871cc609681_jpg.rf.c1bbd66353420220ae80b68c87adb0ac.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 188/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-281-jpg_8927ced5-c93b-482c-a09c-1a6eb25cf04b_jpg.rf.05edf46ee132dbef1777da22ce84f5e9.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 189/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-298-jpg_d0610422-604e-40f4-a4a4-1cd2c4a843c8_jpg.rf.2fedd2b196319d7d3a6757689bd1712f.jpg: 640x640 1 stem-borer, 6.0ms\n", "image 190/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-298-jpg_ec107955-8886-4cec-9eee-8279dbc5931e_jpg.rf.a9d7c7d61f93c09506324d0192202344.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 191/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-3-jpg_4c0d3475-6d06-4637-871f-0959f6cae562_jpg.rf.fb520a1d643c827594c633aaebce0865.jpg: 640x640 1 stem-borer, 6.3ms\n", "image 192/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-3-jpg_64ede291-8d29-4170-81d6-edf5410d2c31_jpg.rf.481d093cf538469ce1145b94a6e72a70.jpg: 640x640 1 stem-borer, 6.3ms\n", "image 193/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-303-jpg_579a0835-8a1d-4af6-84d4-a7a09cf8a188_jpg.rf.7fed523204bf0c9a9835023ae73f7672.jpg: 640x640 1 stem-borer, 6.3ms\n", "image 194/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-310-jpg_8949b25a-5a7d-4bcf-a203-be0712f4d869_jpg.rf.9cc9d9fc68df627876831106a9bca4de.jpg: 640x640 1 brown-planthopper, 1 stem-borer, 6.1ms\n", "image 195/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-39-jpg_41c80b22-3779-4dba-9b08-88bace9d260c_jpg.rf.9211621e0213baae83416a37addfe5fb.jpg: 640x640 1 stem-borer, 6.9ms\n", "image 196/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-39-jpg_bc3a23bd-a65f-4ed6-98e2-41cce543b976_jpg.rf.4d420aedc40cf88ffef4befdb16b7fd7.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 197/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-45-jpg_adc2f333-73ce-4bd9-a56c-062c296ada37_jpg.rf.96c66d0f7ee8e542c394ecb107cee8f6.jpg: 640x640 1 stem-borer, 6.0ms\n", "image 198/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-45-jpg_b0dd770d-ba51-4df0-af65-d8ac44819147_jpg.rf.c794b1393f5533d105780e88f6c2bc11.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 199/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-46-jpg_cfa24ac5-079d-4832-8507-31b8d50ac808_jpg.rf.dcbf4bc1e125214df4042e22a5d383e0.jpg: 640x640 1 stem-borer, 6.7ms\n", "image 200/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-5-jpg_edb52c24-87c8-4579-a482-b8e73b0390a2_jpg.rf.fa1a711cac51db82f419a69056bd87af.jpg: 640x640 1 stem-borer, 6.5ms\n", "image 201/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-56-jpg_3fcf645a-574a-4ade-9fe7-a4e428076b8c_jpg.rf.f3e6d551d8ff2649ee0af75348a680e6.jpg: 640x640 1 stem-borer, 6.8ms\n", "image 202/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-66-jpg_4ec0be4c-ac4c-46b0-a257-68ac7da782c2_jpg.rf.c0795f296375022b1812c5daea0a7699.jpg: 640x640 1 stem-borer, 6.4ms\n", "image 203/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-66-jpg_51975a41-e6f2-486d-b9e9-e01741a2989e_jpg.rf.1ace308b4c5e171894637cf64cc8d9fe.jpg: 640x640 1 stem-borer, 6.0ms\n", "image 204/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-69-jpg_c6d8f6e9-ec7b-491d-ad90-3ac33e0b82f3_jpg.rf.fa8e7bbc2eaceb5a5817696a85aa3532.jpg: 640x640 1 stem-borer, 5.9ms\n", "image 205/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-72-jpg_dc0b49f3-4342-4eed-aed8-dff945cb9efd_jpg.rf.2160dedfcd83cd9e088abd82bcdd4f84.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 206/261 /content/Rice-Pests-1/test/images/STEM-BORER_original_sborer-8-jpg_508846e4-b3a9-43c4-8d5a-a0c62c003674_jpg.rf.fbf8ece345c4ccaf3983c2eb4ccfd0dd.jpg: 640x640 1 stem-borer, 6.4ms\n", "image 207/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-134-jpg_5e5585f2-e72a-4e3a-8c70-2cc597ada8ef_jpg.rf.be7f75ce64a80d978ccf773b286d825d.jpg: 640x640 1 whorl-maggot, 6.3ms\n", "image 208/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-135-jpg_06856dbe-7c5a-4dda-a2d5-d6af7fedca3b_jpg.rf.673beef5a2a0939d30b50373f62611e4.jpg: 640x640 1 whorl-maggot, 6.0ms\n", "image 209/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-135-jpg_6ce1619d-eaf4-4615-85b2-3b868b181549_jpg.rf.60629ab6cc76c15aa4686aa6ec222ad1.jpg: 640x640 1 whorl-maggot, 6.0ms\n", "image 210/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-142-jpg_41b1efba-e249-4c9b-910f-c8cb709d8af8_jpg.rf.aa14a0b693c07a649e866c127d860ea5.jpg: 640x640 3 whorl-maggots, 6.7ms\n", "image 211/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-144-jpg_03a204b7-5fb6-42de-8dfc-8e3d9ef77b26_jpg.rf.3aa6a4d12cc00f76b19483ac43661549.jpg: 640x640 2 whorl-maggots, 6.1ms\n", "image 212/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-144-jpg_c5b55875-d69d-461f-8555-a5fdb7202ba3_jpg.rf.76c02c551fe6335c9ce895b2270f1a23.jpg: 640x640 1 whorl-maggot, 7.8ms\n", "image 213/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-146-jpg_186c7841-b200-4e83-acac-067aa46e5a1e_jpg.rf.aad0b817a4bf280660260ca48b05d47a.jpg: 640x640 1 whorl-maggot, 8.3ms\n", "image 214/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-149-jpg_04f4f3dd-f3a0-498b-8181-e80ea5ea80c4_jpg.rf.6a1980adfdf99737a20993aa9eebfa83.jpg: 640x640 1 whorl-maggot, 6.5ms\n", "image 215/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-149-jpg_d36eebf2-9c47-43d5-92d0-7d02c23022a7_jpg.rf.dc809b3fd576675f09cbf5cced84f201.jpg: 640x640 1 whorl-maggot, 6.2ms\n", "image 216/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-150-jpg_98e57768-eb42-4abf-83c7-bd157dcb93c8_jpg.rf.7eafd6e643b6a181aa4853c5328b7745.jpg: 640x640 2 whorl-maggots, 6.1ms\n", "image 217/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-153-jpg_f8f80d14-fe5c-4664-bca4-7ac82066ddef_jpg.rf.ea140f9ca9e6dbbaef5910b7e0fa2636.jpg: 640x640 2 whorl-maggots, 6.0ms\n", "image 218/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-155-jpg_1213175b-c357-4567-be55-0c6020fc1655_jpg.rf.0481e3f4d0cc910349dbd555e6e96b9c.jpg: 640x640 2 whorl-maggots, 6.2ms\n", "image 219/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-155-jpg_effeeb6d-6252-4be7-809c-1325d2425f75_jpg.rf.036070ffa35c5332f97a10b1c97192d9.jpg: 640x640 2 whorl-maggots, 6.1ms\n", "image 220/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-162-jpg_4615040e-6a19-46bd-8fb9-20e3cfc99365_jpg.rf.9c5d96ff51401712d157abad6d050c2f.jpg: 640x640 2 whorl-maggots, 5.8ms\n", "image 221/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-162-jpg_c56edb84-a7f0-4849-ac1f-e924bc65faaf_jpg.rf.b827f3d57ffd0cba987c135030c106cb.jpg: 640x640 2 whorl-maggots, 7.1ms\n", "image 222/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-163-jpg_c1c411ca-becd-49e8-97f4-7ea3d133eede_jpg.rf.8f8a74f79206994f69ae59e571dbcba6.jpg: 640x640 1 whorl-maggot, 10.9ms\n", "image 223/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-164-jpg_27b49183-5604-4384-a6f2-52c3691ff462_jpg.rf.08bc8f9ae0ba106d31cbb6fe2abf5583.jpg: 640x640 2 whorl-maggots, 6.2ms\n", "image 224/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-165-jpg_01e88e28-f414-43b6-8f9e-742424987573_jpg.rf.cd8e6abdd07c3ac08e671fd8c7e70308.jpg: 640x640 1 whorl-maggot, 6.8ms\n", "image 225/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-165-jpg_1e1c6fa0-8573-4f3f-958d-3986f9d1aa60_jpg.rf.c06db98a0c58a0f2b9daf4eb35186088.jpg: 640x640 2 whorl-maggots, 6.9ms\n", "image 226/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-170-jpg_7deb58e2-d131-45d9-9f1b-1b80aea70519_jpg.rf.e1023af6f5bc50e34a3f60624ebb3d24.jpg: 640x640 2 whorl-maggots, 6.2ms\n", "image 227/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-19-jpg_fe194cf8-724e-41e1-9390-b05904f8df15_jpg.rf.2cf4fe24f66d861b8435a5ef6ae740c4.jpg: 640x640 2 whorl-maggots, 6.4ms\n", "image 228/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-36-jpg_b76828d1-8748-4374-bb62-960d2e0169e6_jpg.rf.93e3b6508edd8c6119c3b8453f11abff.jpg: 640x640 18 whorl-maggots, 6.8ms\n", "image 229/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-36-jpg_d1148779-8af4-4a6a-b7b0-e50453af4582_jpg.rf.9e0c7cb18e0e3591a9f04f587cf3c961.jpg: 640x640 15 whorl-maggots, 9.0ms\n", "image 230/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-36-jpg_dcc53ff0-007c-43b3-84f9-530590f67520_jpg.rf.0b9b40454f6e82e0e3e237b618684ab4.jpg: 640x640 18 whorl-maggots, 6.4ms\n", "image 231/261 /content/Rice-Pests-1/test/images/WHORL-MAGGOTS_original_whorl-6-jpg_61f65147-4344-42bc-a3b6-9473138272bb_jpg.rf.ed031b40f0e32b2d3f415d9f8b88b626.jpg: 640x640 3 whorl-maggots, 6.9ms\n", "image 232/261 /content/Rice-Pests-1/test/images/bhopper-124-_jpg.rf.33ca82d305c10f261a722083323d4528.jpg: 640x640 4 brown-planthoppers, 10.8ms\n", "image 233/261 /content/Rice-Pests-1/test/images/bhopper-140-_jpg.rf.de3a4f627626ff2915f49ac3f22aded8.jpg: 640x640 1 brown-planthopper, 7.6ms\n", "image 234/261 /content/Rice-Pests-1/test/images/bhopper-185-_jpg.rf.54e46374481fd9662bd407ec97a1a18d.jpg: 640x640 1 brown-planthopper, 6.5ms\n", "image 235/261 /content/Rice-Pests-1/test/images/bhopper-291-_jpg.rf.523f649d770d7c148565e6a94734f075.jpg: 640x640 1 brown-planthopper, 6.2ms\n", "image 236/261 /content/Rice-Pests-1/test/images/bhopper-306-_jpg.rf.d9d27e5f9bea0b1a9c6fec7e0cb2f780.jpg: 640x640 4 brown-planthoppers, 6.3ms\n", "image 237/261 /content/Rice-Pests-1/test/images/bhopper-322-_jpg.rf.73fe178d63904068b09221805169efff.jpg: 640x640 1 brown-planthopper, 7.1ms\n", "image 238/261 /content/Rice-Pests-1/test/images/lfolder-11-_jpg.rf.6a13206495d5cea3b4802bff29f6fa6f.jpg: 640x640 1 leaf-folder, 8.1ms\n", "image 239/261 /content/Rice-Pests-1/test/images/lfolder-110-_jpg.rf.45c2eb9e21776b2b568bef997489b9d8.jpg: 640x640 (no detections), 6.2ms\n", "image 240/261 /content/Rice-Pests-1/test/images/lfolder-14-_jpg.rf.01e16ff463ea06898f634be9191ad134.jpg: 640x640 1 leaf-folder, 6.0ms\n", "image 241/261 /content/Rice-Pests-1/test/images/lfolder-15-_jpg.rf.3a4d947367ff81fae41fe3dab9652903.jpg: 640x640 1 leaf-folder, 6.0ms\n", "image 242/261 /content/Rice-Pests-1/test/images/rbug-101-_jpg.rf.23bf21d13d3970ad6e88c3244e9439c7.jpg: 640x640 1 rice-bug, 6.8ms\n", "image 243/261 /content/Rice-Pests-1/test/images/rbug-103-_jpg.rf.08c7961a9ffcdbf14ac1be6c318f3682.jpg: 640x640 1 rice-bug, 6.7ms\n", "image 244/261 /content/Rice-Pests-1/test/images/rbug-13-_jpg.rf.110175b89e2fca2c9f6637a8022142a5.jpg: 640x640 1 rice-bug, 6.8ms\n", "image 245/261 /content/Rice-Pests-1/test/images/rbug-137-_jpg.rf.d547b73a71bd19cd9091489012d6ce80.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 246/261 /content/Rice-Pests-1/test/images/rbug-237-_jpg.rf.cf8fb80b22af687a68d11b93ffe346d1.jpg: 640x640 2 rice-bugs, 6.6ms\n", "image 247/261 /content/Rice-Pests-1/test/images/rbug-240-_jpg.rf.34192e79f1b5ebebaaedcc653099ea34.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 248/261 /content/Rice-Pests-1/test/images/rbug-260-_jpg.rf.788fe10241f66040f8851bc6b2919eb3.jpg: 640x640 1 rice-bug, 6.1ms\n", "image 249/261 /content/Rice-Pests-1/test/images/rbug-28-_jpg.rf.36a93c098bbbd7c5ac1b5ce9ffaaeb0a.jpg: 640x640 3 rice-bugs, 6.3ms\n", "image 250/261 /content/Rice-Pests-1/test/images/rbug-299-_jpg.rf.b8c51eb4032f1026b4097aedf9fd1219.jpg: 640x640 1 rice-bug, 5.9ms\n", "image 251/261 /content/Rice-Pests-1/test/images/rbug-81-_jpg.rf.3083ce113d9ddebf4d74bcb714e57e99.jpg: 640x640 2 rice-bugs, 6.2ms\n", "image 252/261 /content/Rice-Pests-1/test/images/rbug-94-_jpg.rf.bb364bf42b54b7d3254b0c945bf8f8cd.jpg: 640x640 1 rice-bug, 6.0ms\n", "image 253/261 /content/Rice-Pests-1/test/images/sborer-102-_jpg.rf.35d728d496b2a912437c39022cc67e9c.jpg: 640x640 1 stem-borer, 6.2ms\n", "image 254/261 /content/Rice-Pests-1/test/images/sborer-143-_jpg.rf.945fed0a6a1e8c1c86f7b811f9f408ac.jpg: 640x640 1 stem-borer, 6.1ms\n", "image 255/261 /content/Rice-Pests-1/test/images/sborer-200-_jpg.rf.1adecfeff0d585be653b157ff0d9d8a0.jpg: 640x640 6 stem-borers, 6.4ms\n", "image 256/261 /content/Rice-Pests-1/test/images/sborer-245-_jpg.rf.06aadfab5536a51e28a4e3b1011996fc.jpg: 640x640 1 stem-borer, 6.6ms\n", "image 257/261 /content/Rice-Pests-1/test/images/sborer-309-_jpg.rf.540d75cdf97a219cee23cec2c4192f0e.jpg: 640x640 1 stem-borer, 7.6ms\n", "image 258/261 /content/Rice-Pests-1/test/images/sborer-43-_jpg.rf.1e8e2d7e76096ca5a20bbbc5ba3f242b.jpg: 640x640 1 stem-borer, 11.2ms\n", "image 259/261 /content/Rice-Pests-1/test/images/sborer-68-_jpg.rf.cc45734a25868fa736d57b924718b2b5.jpg: 640x640 1 stem-borer, 7.9ms\n", "image 260/261 /content/Rice-Pests-1/test/images/whorl-32-_jpg.rf.0ae7dcebc083569ac83d8b39c10bf616.jpg: 640x640 2 whorl-maggots, 6.2ms\n", "image 261/261 /content/Rice-Pests-1/test/images/whorl-48-_jpg.rf.0e6a375ebeba86c322c8de0db4e9e291.jpg: 640x640 1 green-leafhopper, 1 whorl-maggot, 7.8ms\n", "Speed: 1.7ms preprocess, 6.9ms inference, 1.4ms postprocess per image at shape (1, 3, 640, 640)\n", "Results saved to \u001b[1mruns/detect/predict\u001b[0m\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "V3YbJOv_Y0XW"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "zWrnEj9lY0Uq"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "tEz2nNmbY0R1"}, "execution_count": null, "outputs": []}]}