#!/usr/bin/env python3
"""
Test script to verify API key loading and weather API functionality
"""

import os
from dotenv import load_dotenv
from chat import getweatherdata, GROQ_API_KEY, WEATHER_API_KEY

def test_env_loading():
    """Test if environment variables are loaded correctly"""
    print("Testing environment variable loading...")
    
    # Load environment variables
    load_dotenv()
    
    # Check GROQ API key
    groq_key = os.getenv("GROQ_API_KEY")
    print(f"GROQ_API_KEY loaded: {'✓' if groq_key else '✗'}")
    if groq_key:
        print(f"GROQ_API_KEY (first 10 chars): {groq_key[:10]}...")
    
    # Check Weather API key
    weather_key = os.getenv("WEATHER_API_KEY")
    print(f"WEATHER_API_KEY loaded: {'✓' if weather_key else '✗'}")
    if weather_key:
        print(f"WEATHER_API_KEY: {weather_key}")
    
    # Check if keys are loaded in chat.py module
    print(f"GROQ_API_KEY in chat.py: {'✓' if GROQ_API_KEY else '✗'}")
    print(f"WEATHER_API_KEY in chat.py: {'✓' if WEATHER_API_KEY else '✗'}")
    
    return groq_key and weather_key

def test_weather_api():
    """Test weather API functionality"""
    print("\nTesting weather API...")
    
    try:
        # Test different days
        test_days = ["Today", "Tomorrow", "2 days"]
        
        for day in test_days:
            print(f"\nTesting weather data for: {day}")
            weather_data = getweatherdata(day)
            
            if weather_data:
                date, temperature, humidity, windspeed, pressure, description, temp_max, temp_min = weather_data
                print(f"✓ Weather data retrieved successfully:")
                print(f"  Date: {date}")
                print(f"  Temperature: {temperature}°C")
                print(f"  Humidity: {humidity}%")
                print(f"  Wind Speed: {windspeed} m/s")
                print(f"  Pressure: {pressure} hPa")
                print(f"  Description: {description}")
                print(f"  Max Temp: {temp_max}°C")
                print(f"  Min Temp: {temp_min}°C")
            else:
                print(f"✗ Failed to retrieve weather data for {day}")
                return False
        
        return True
        
    except Exception as e:
        print(f"✗ Error testing weather API: {e}")
        return False

def main():
    """Main test function"""
    print("=" * 50)
    print("API Key and Weather API Test")
    print("=" * 50)
    
    # Test environment loading
    env_success = test_env_loading()
    
    if not env_success:
        print("\n❌ Environment variable loading failed!")
        return
    
    # Test weather API
    weather_success = test_weather_api()
    
    print("\n" + "=" * 50)
    if env_success and weather_success:
        print("✅ All tests passed! API keys are loaded correctly and weather API is working.")
    else:
        print("❌ Some tests failed. Please check the issues above.")
    print("=" * 50)

if __name__ == "__main__":
    main()
